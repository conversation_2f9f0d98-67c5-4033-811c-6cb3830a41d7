using Esigno.XyzmoController;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ProcessPoint.Rest.Esigno.Models;
using ProcessPoint.Rest.Esigno.Services;

namespace ProcessPoint.Rest.Esigno.Controllers;

[Authorize]
[Route("api/[controller]/[action]")]    
[ApiController]
public class EsignoController : ControllerBase
{
    // GET api/<ValuesController>/5
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(EnvelopeState), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> GetSessionOpenLinks([FromRoute] string id)
    {
        EsignoService svc = new();
        return await svc.GetSessionOpenLinksAsync(id) ? Ok(new List<OpenLink>(svc.OpenLinks)) : BadRequest(new());
    }

    [HttpPost]
    [ProducesResponseType(typeof(EnvelopeState), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> UpdateSession([FromBody] Envelope rq)
    {
        EsignoService svc = new(rq);
        return await svc.UpdateSessionAsync() ? Ok(svc.EnvelopeState) : BadRequest(new());
    }
    
    [HttpPost]
    [ProducesResponseType(typeof(EnvelopeState), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> StartSession([FromBody] Envelope rq)
    {
        EsignoService svc = new(rq);
        return await svc.StartSessionAsync() ? Ok(svc.EnvelopeState) : BadRequest(new());
    }

    [HttpGet("{id}")]
    [ProducesResponseType(typeof(EnvelopeState), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> GetSessionStatus([FromRoute] string id)
    {
        EsignoService svc = new();
        return await svc.GetSessionStatusAsync(id) ? Ok(svc.EnvelopeState) : BadRequest(new());
    }

    [HttpGet("{id}")]
    [ProducesResponseType(typeof(EnvelopeState), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> KillSession([FromRoute] string id)
    {
        EsignoService svc = new();
        return await svc.KillSessionAsync(id) ? Ok(svc.EnvelopeState) : BadRequest(new());
    }
    
    [HttpPost]
    [ProducesResponseType(typeof(List<Token>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(object), StatusCodes.Status400BadRequest)]
    public async Task<object> GetTokens([FromBody] Base64Request request)
    {
        var helper = new TokenHelper();
        string base64Pdf;
        if (request.Base64Pdf.Contains("<file>"))
        {
            base64Pdf = XmlFileParser.GetContentFromXml(request.Base64Pdf);
        }
        else
        {
            base64Pdf = request.Base64Pdf;
        }
        var ids = helper.ExtractSignatureIdsFromBase64(base64Pdf);
        List<Token> tokenIds = new();
        ids.ForEach(x => tokenIds.Add(new Token() { TokenId = x }));

        return Ok(tokenIds);
    }
}

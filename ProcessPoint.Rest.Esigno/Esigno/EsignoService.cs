using System.Xml.Serialization;
using Esigno.XyzmoController;
using ProcessPoint.Rest.Esigno.Models;
using ProcessPoint.Rest.Esigno.Services;
using AuthenticationType = ProcessPoint.Rest.Esigno.Models.AuthenticationType;
using DestinationType = ProcessPoint.Rest.Esigno.Models.DestinationType;
using OpenLink = Esigno.XyzmoController.OpenLink;
using Task = ProcessPoint.Rest.Esigno.Models.Task;
using TaskGroupType = ProcessPoint.Rest.Esigno.Models.TaskGroupType;
using TaskParamType = ProcessPoint.Rest.Esigno.Models.TaskParamType;


public class EsignoService
{
    const string URL = "https://api.sandbox.esigno.io/key-48BB357E1246FCCAF7EBD6A82BAF6275A5E913642DF9BA2B8B0EA1932F2DABB5/XyzmoController.asmx";
    XyzmoControllerSoapClient esignoClient = new(new(), URL);

    // Statické instance XmlSerializer pro opakované použití
    private static readonly XmlSerializer _sessionConfigSerializer = new(typeof(SessionConfiguration));
    private static readonly XmlSerializer _sessionResultSerializer = new(typeof(SessionResult));

    private SessionConfiguration _sessionConfiguration = new();
    private SessionResult _sessionResult;
    public List<OpenLink> OpenLinks;
    public EnvelopeState EnvelopeState;

    // Pomocná metoda pro serializaci objektu do XML řetězce
    private string SerializeToXml<T>(T obj, XmlSerializer serializer)
    {
        using var ms = new MemoryStream();
        serializer.Serialize(ms, obj);
        ms.Position = 0;
        using var reader = new StreamReader(ms);
        return reader.ReadToEnd();
    }

    // Pomocná metoda pro deserializaci XML řetězce na objekt
    private T DeserializeFromXml<T>(string xml, XmlSerializer serializer)
    {
        using var reader = new StringReader(xml);
        return (T)serializer.Deserialize(reader);
    }

    // Pomocná metoda pro zpracování odpovědi ze serveru
    private bool ProcessSessionResponse(string response)
    {
        if (string.IsNullOrEmpty(response))
            return false;
        try
        {
            _sessionResult = DeserializeFromXml<SessionResult>(response, _sessionResultSerializer);
            EnvelopeState = new(_sessionResult);
            var openLinks = esignoClient.xc_session_open_linksAsync(new GetOpenLinksQuery() { SessionId = EnvelopeState.EnvelopeId }).Result.Body.xc_session_open_linksResult.GoOpenLinks.ToList();
            EnvelopeState.Signatories.ForEach(x => x.UserAccessLink = openLinks.Single(y => y.AttendeeName == x.Name).Uri);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deserializing response: {ex.Message}");
            Console.WriteLine($"Response: {response}");
            return false;
        }
    }
    
    private bool ProcessKillSessionResponse(string response)
    {
        if (string.IsNullOrEmpty(response))
            return false;
        try
        {
            _sessionResult = DeserializeFromXml<SessionResult>(response, _sessionResultSerializer);
            EnvelopeState = new(_sessionResult);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deserializing response: {ex.Message}");
            Console.WriteLine($"Response: {response}");
            return false;
        }
    }
    
    private bool ProcessUpdateSessionResponse(string response)
    {
        if (string.IsNullOrEmpty(response))
            return false;
        try
        {
            _sessionResult = DeserializeFromXml<SessionResult>(response, _sessionResultSerializer);
            EnvelopeState = new(_sessionResult);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error deserializing response: {ex.Message}");
            Console.WriteLine($"Response: {response}");
            return false;
        }
    }
    
    private bool ProcessOpenLinks(List<OpenLink> openLinks)
    {
        if (openLinks == null || openLinks.Count == 0)
            return false;

        try
        {
            openLinks.ForEach(x => Console.WriteLine(x.Uri));
            OpenLinks = openLinks;
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing open links: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> UpdateSessionAsync()
    {
        try
        {
            string requestXml = SerializeToXml(_sessionConfiguration, _sessionConfigSerializer);
            xc_update_sessionResponse? responseXml = await esignoClient.xc_update_sessionAsync(requestXml);
            return ProcessUpdateSessionResponse(responseXml.Body.xc_update_sessionResult);
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> StartSessionAsync()
    {
        try
        {
            string requestXml = SerializeToXml(_sessionConfiguration, _sessionConfigSerializer);
            xc_start_sessionResponse? responseXml = await esignoClient.xc_start_sessionAsync(requestXml);
            return ProcessSessionResponse(responseXml.Body.xc_start_sessionResult);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return false;
        }
    }

    public async Task<bool> GetSessionStatusAsync(string id)
    {
        try
        {
            xc_get_session_statusResponse? responseXml = await esignoClient.xc_get_session_statusAsync(id);
            return ProcessUpdateSessionResponse(responseXml.Body.xc_get_session_statusResult);
        }
        catch
        {
            return false;
        }
    }

    
    public async Task<bool> GetSessionOpenLinksAsync(string id)
    {
        try
        {
            xc_session_open_linksResponse? response = await esignoClient.xc_session_open_linksAsync(new GetOpenLinksQuery() { SessionId = id });
            return ProcessOpenLinks(response.Body.xc_session_open_linksResult.GoOpenLinks.ToList());
        }
        catch
        {
            return false;
        }
    }

    public async Task<bool> KillSessionAsync(string id)
    {
        try
        {
            xc_kill_sessionResponse? responseXml = await esignoClient.xc_kill_sessionAsync(id);
            return ProcessKillSessionResponse(responseXml.Body.xc_kill_sessionResult);
        }
        catch
        {
            return false;
        }
    }

    public EsignoService(Envelope rq)
    {
        List<TaskGroup> sessionTasks = new();

        List<TransactionProcess> transactionProcesses = new();

        foreach (Signatory sgr in rq.Signatories)
        {
            TransactionProcess tProcess = new();
            OneTimeToken transactionToken = new();
            transactionToken.DestinationAddress = sgr.Phone;
            transactionToken.ValidityInSecond = 600;
            transactionToken.DestinationType = DestinationType.SmsPhoneNumber;

            Transaction transaction = new();
            transaction.AuthenticationType = AuthenticationType.OneTimeToken;
            transaction.OneTimeToken = transactionToken;
            tProcess.Transactions = new List<Transaction>() { transaction };
            transactionProcesses.Add(tProcess);
        }

        foreach (Document doc in rq.Documents)
        {
            TaskGroup tg = new()
            {
                Type = TaskGroupType.Sign,
                Id = doc.DocumentId.ToString(),
                Name = doc.Name
                //DocumentData = doc.DocumentData,
                //DocumentUrl = doc.DocumentUrl
            };
            if (string.IsNullOrEmpty(doc.DocumentUrl))
            {
                if (doc.DocumentData.Contains("<file>"))
                {
                    tg.DocumentData = XmlFileParser.GetContentFromXml(doc.DocumentData).Select(x => (byte)x).ToArray();
                }
                else
                {
                    tg.DocumentData = doc.DocumentData.Select(x => (byte)x).ToArray();
                }
            }
            if (!string.IsNullOrEmpty(doc.DocumentUrl)) tg.DocumentUrl = doc.DocumentUrl;

            List<Task> tasks = new();
            foreach (Token token in doc.Tokens)
            {
                Task task = new();
                task.Id = token.TokenId;
                task.Width = 100;
                task.Height = 30;
                string SignatureType = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).SignatureType;
                task.Param = new List<TaskParam>()
                {
                    new TaskParam() { Name = TaskParamType.SigType, Value = SignatureType },
                    new TaskParam() { Name = TaskParamType.Positioning, Value = "onPage" },
                    new TaskParam() { Name = TaskParamType.Enabled, Value = token.Enabled.ToString() },
                    new TaskParam() { Name = TaskParamType.Completed, Value = "0" },
                    new TaskParam() { Name = TaskParamType.Required, Value = token.Required.ToString() },
                    new TaskParam() { Name = TaskParamType.UserId, Value = token.SignatoryId.ToString() },
                    new TaskParam() { Name = TaskParamType.DisplayName, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).DisplayName  },
                    new TaskParam() { Name = TaskParamType.SignatoryDestinationType, Value = "Email" },
                    new TaskParam() { Name = TaskParamType.SignatoryDestinationAddress, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).Email },
                    new TaskParam() { Name = TaskParamType.SignatoryName, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).Name },
                    new TaskParam() { Name = TaskParamType.SignpadHeaderText, Value = token.SignpadHeaderText }
                };
                if (SignatureType == "electronicSignature")
                {
                    task.Param.Add( new TaskParam() {Name = TaskParamType.CertificateThumbprint, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).CertificateThumbprint});
                    task.Param.Add( new TaskParam() { Name = TaskParamType.UseTimestamps, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).UseTimestamps });
                    task.Param.Add( new TaskParam() { Name = TaskParamType.UseTimestampSet, Value = rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).UseTimestampSet });
                }
                if (SignatureType == "transactionProcess")
                    task.TransactionProcess = transactionProcesses.Single(y => y.Transactions.Where(z => z.OneTimeToken.DestinationAddress == rq.Signatories.Single(x => x.SignatoryId == token.SignatoryId).Phone).Count() > 0);
                tasks.Add(task);
            }
            tg.Tasks = tasks;
            sessionTasks.Add(tg);
        }
        _sessionConfiguration.SessionLabel = rq.Name; //"Zkušební session";
        _sessionConfiguration.SessionTimeToLiveInMinutes = rq.TimeToLiveInMinutes; // 15;
        _sessionConfiguration.SsoId = rq.ContextId.ToString();
        _sessionConfiguration.SequenceMode =  
            rq.SequenceMode == "SequenceEnforced" ? SequenceModeType.SequenceEnforced 
            : rq.SequenceMode == "SequenceOnlyRequired" ? SequenceModeType.SequenceOnlyRequired 
            : SequenceModeType.NoSequenceEnforced; 
        _sessionConfiguration.DefaultsSet = rq.DefaultSet; //"CP1-NOSIGSTRINGCLEAR";
        _sessionConfiguration.SessionTasks = sessionTasks;
    }

    public EsignoService()
    {

    }
}
